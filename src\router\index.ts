import { createRouter, createWebHistory } from 'vue-router'

export default createRouter({
  history: createWebHistory(),
  routes: [
    { name: 'home', path: '/', component: () => import('@/pages/home/<USER>') },
    { name: 'hospital', path: '/hospital', component: () => import('@/pages/hospital/index.vue') }
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  }
})